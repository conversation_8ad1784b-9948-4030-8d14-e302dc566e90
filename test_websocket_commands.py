#!/usr/bin/env python3
"""
Test WebSocket Terminal Commands
"""

import asyncio
import websockets
import json
from datetime import datetime

async def test_websocket_commands():
    """Test specific WebSocket terminal commands"""
    uri = "ws://localhost:8002/ws/scanner"
    
    try:
        print("🔌 Connecting to A.T.L.A.S. WebSocket terminal...")
        
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to WebSocket terminal!")
            
            # Wait for welcome messages
            print("\n📨 Waiting for welcome messages...")
            for i in range(3):
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                    data = json.loads(message)
                    print(f"📨 Welcome {i+1}: {data.get('type')} - {data.get('message', '')[:50]}...")
                except asyncio.TimeoutError:
                    break
                except Exception as e:
                    print(f"❌ Error receiving welcome: {e}")
            
            # Test specific commands
            test_commands = ["help", "status", "health", "quote AAPL"]
            
            for cmd in test_commands:
                print(f"\n🧪 Testing command: '{cmd}'")
                
                # Send command
                command_msg = {
                    "type": "command",
                    "command": cmd
                }
                
                await websocket.send(json.dumps(command_msg))
                print(f"📤 Sent: {command_msg}")
                
                # Wait for response
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response_data = json.loads(response)
                    
                    print(f"📨 Response type: {response_data.get('type')}")
                    
                    if response_data.get('type') == 'command_response':
                        output = response_data.get('output', '')
                        print(f"✅ Command response received:")
                        print(f"   Output preview: {output[:100]}...")
                    else:
                        print(f"⚠️  Unexpected response type: {response_data.get('type')}")
                        print(f"   Message: {response_data.get('message', '')[:50]}...")
                        
                except asyncio.TimeoutError:
                    print("⏰ No response received within 5 seconds")
                except Exception as e:
                    print(f"❌ Error processing response: {e}")
                
                # Wait between commands
                await asyncio.sleep(1)
            
            print("\n🎯 Command testing completed!")
            
    except Exception as e:
        print(f"❌ WebSocket connection failed: {e}")

if __name__ == "__main__":
    print("🧪 Testing A.T.L.A.S. WebSocket Terminal Commands")
    print("=" * 50)
    
    asyncio.run(test_websocket_commands())
