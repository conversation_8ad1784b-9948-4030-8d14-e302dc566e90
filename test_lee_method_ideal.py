#!/usr/bin/env python3
"""
Test Lee Method with Ideal Conditions
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_ideal_conditions():
    """Test Lee Method with ideal signal conditions"""
    try:
        print("🎯 Lee Method Ideal Conditions Test")
        print("=" * 40)
        
        from atlas_lee_method import LeeMethodScanner
        
        # Create scanner with relaxed settings for testing
        scanner = LeeMethodScanner()
        scanner.market_hours_only = False
        scanner.confidence_threshold = 0.5  # 50% threshold
        scanner.allow_weak_signals = True
        
        print("✅ Scanner configured for ideal conditions test")
        
        # Create ideal test data that should trigger all criteria
        ideal_data = {
            "symbol": "AAPL",
            "price": 220.0,  # Strong upward move
            "current_price": 220.0,
            "close": 220.0,
            "volume": 25000000,  # 2.5x average volume
            "avg_volume": 10000000,
            "high": 221.0,
            "low": 215.0,  # Good range
            "open": 216.0,  # Gap up
            "timestamp": datetime.now().isoformat(),
            
            # Additional technical indicators that <PERSON> Method might use
            "rsi": 65,  # Moderate RSI
            "macd": 1.5,  # Positive MACD
            "moving_avg_20": 210.0,  # Above 20-day MA
            "moving_avg_50": 205.0,  # Above 50-day MA
            "bollinger_upper": 225.0,
            "bollinger_lower": 200.0,
            "support_level": 215.0,
            "resistance_level": 225.0
        }
        
        print(f"📊 Ideal test data created with strong bullish signals")
        
        # Test with ideal conditions
        print(f"\n🔍 Testing with ideal conditions...")
        signal = await scanner.scan_symbol("AAPL", ideal_data)
        
        if signal:
            print(f"🎯 SUCCESS! Signal generated with ideal conditions!")
            print(f"   Symbol: {signal.symbol}")
            print(f"   Direction: {signal.signal_direction}")
            print(f"   Strength: {signal.signal_strength}")
            print(f"   Confidence: {signal.confidence:.2%}")
            print(f"   Entry Price: ${signal.entry_price}")
            print(f"   Target Price: ${signal.target_price}")
            print(f"   Stop Loss: ${signal.stop_loss}")
            print(f"   Risk/Reward: {signal.risk_reward_ratio:.2f}")
        else:
            print("❌ No signal generated even with ideal conditions")
            
            # Let's check what the analysis methods return
            print("\n🔍 Analyzing why no signal was generated...")
            
            try:
                trend_result = await scanner._analyze_trend_confirmation("AAPL", ideal_data)
                print(f"📈 Trend: confirmed={trend_result.get('confirmed')}, score={trend_result.get('score')}")
                
                volume_result = await scanner._analyze_volume_validation("AAPL", ideal_data)
                print(f"📊 Volume: validated={volume_result.get('validated')}, ratio={volume_result.get('ratio')}")
                
                pattern_result = await scanner._analyze_technical_pattern("AAPL", ideal_data)
                print(f"📉 Pattern: confirmed={pattern_result.get('confirmed')}, type={pattern_result.get('pattern_type')}")
                
                confidence = scanner._calculate_confidence(trend_result, volume_result, pattern_result)
                print(f"🎯 Total confidence: {confidence:.2%} (threshold: {scanner.confidence_threshold:.2%})")
                
            except Exception as e:
                print(f"❌ Analysis error: {e}")
        
        # Test with even more relaxed settings
        print(f"\n🔧 Testing with very relaxed settings...")
        scanner.confidence_threshold = 0.1  # 10% threshold
        
        signal = await scanner.scan_symbol("AAPL", ideal_data)
        
        if signal:
            print(f"🎯 SUCCESS with relaxed settings!")
            print(f"   Confidence: {signal.confidence:.2%}")
        else:
            print("❌ Still no signal with relaxed settings")
        
        print(f"\n✅ Ideal conditions test completed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_ideal_conditions())
