#!/usr/bin/env python3
"""
Simple Lee Method Test - Force Signal Generation
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_simple_signal():
    """Test Lee Method with forced signal generation"""
    try:
        print("🧪 Simple Lee Method Signal Test")
        print("=" * 40)
        
        from atlas_lee_method import LeeMethodScanner, LeeMethodSignal, SignalDirection, SignalStrength
        
        # Create scanner
        scanner = LeeMethodScanner()
        scanner.market_hours_only = False
        scanner.confidence_threshold = 0.1  # Very low threshold
        scanner.allow_weak_signals = True
        
        print("✅ Scanner configured for testing")
        
        # Create comprehensive test data
        test_data = {
            "symbol": "AAPL",
            "price": 210.0,
            "current_price": 210.0,
            "close": 210.0,
            "volume": 15000000,
            "avg_volume": 10000000,
            "high": 212.0,
            "low": 208.0,
            "open": 209.0,
            "timestamp": datetime.now().isoformat()
        }
        
        print(f"📊 Test data: {test_data}")
        
        # Test the analysis methods individually
        print("\n🔍 Testing individual analysis methods...")
        
        try:
            trend_result = await scanner._analyze_trend_confirmation("AAPL", test_data)
            print(f"📈 Trend analysis: {trend_result}")
        except Exception as e:
            print(f"❌ Trend analysis error: {e}")
            trend_result = {"confirmed": True, "alignment": "bullish", "strength": 0.7}
            
        try:
            volume_result = await scanner._analyze_volume_validation("AAPL", test_data)
            print(f"📊 Volume analysis: {volume_result}")
        except Exception as e:
            print(f"❌ Volume analysis error: {e}")
            volume_result = {"validated": True, "ratio": 1.5, "strength": 0.8}
            
        try:
            pattern_result = await scanner._analyze_technical_pattern("AAPL", test_data)
            print(f"📉 Pattern analysis: {pattern_result}")
        except Exception as e:
            print(f"❌ Pattern analysis error: {e}")
            pattern_result = {
                "confirmed": True, 
                "pattern_type": "bullish_breakout",
                "sr_level": 205.0,
                "stop_loss": 205.0,
                "target_price": 220.0,
                "risk_reward": 2.0
            }
        
        # Calculate confidence manually
        confidence = scanner._calculate_confidence(trend_result, volume_result, pattern_result)
        print(f"🎯 Calculated confidence: {confidence:.2%}")
        
        # Try to create a signal manually
        if confidence >= scanner.confidence_threshold:
            print("✅ Confidence meets threshold, creating signal...")
            
            signal = LeeMethodSignal(
                symbol="AAPL",
                timestamp=datetime.now(),
                signal_direction=SignalDirection.BUY,
                signal_strength=SignalStrength.STRONG,
                confidence=confidence,
                current_price=210.0,
                trend_confirmation=trend_result.get('confirmed', True),
                volume_validation=volume_result.get('validated', True),
                technical_pattern=pattern_result.get('confirmed', True),
                trend_alignment=trend_result.get('alignment', 'bullish'),
                volume_ratio=volume_result.get('ratio', 1.5),
                pattern_type=pattern_result.get('pattern_type', 'test_pattern'),
                support_resistance_level=pattern_result.get('sr_level', 205.0),
                entry_price=210.0,
                stop_loss=pattern_result.get('stop_loss', 205.0),
                target_price=pattern_result.get('target_price', 220.0),
                risk_reward_ratio=pattern_result.get('risk_reward', 2.0),
                timeframe="daily"
            )
            
            print(f"🎯 Manual signal created:")
            print(f"   Symbol: {signal.symbol}")
            print(f"   Direction: {signal.signal_direction}")
            print(f"   Strength: {signal.signal_strength}")
            print(f"   Confidence: {signal.confidence:.2%}")
            print(f"   Is Valid: {signal.is_valid}")
            
        else:
            print(f"❌ Confidence {confidence:.2%} below threshold {scanner.confidence_threshold:.2%}")
        
        # Now test the full scan_symbol method
        print(f"\n🔍 Testing full scan_symbol method...")
        signal = await scanner.scan_symbol("AAPL", test_data)
        
        if signal:
            print(f"🎯 Full scan signal generated!")
            print(f"   Confidence: {signal.confidence:.2%}")
            print(f"   Direction: {signal.signal_direction}")
        else:
            print("❌ Full scan did not generate signal")
            
        print(f"\n✅ Simple test completed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_simple_signal())
