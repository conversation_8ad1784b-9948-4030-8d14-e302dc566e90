#!/usr/bin/env python3
"""
Test Lee Method Scanner Functionality
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_lee_method_scanner():
    """Test the Lee Method scanner functionality"""
    try:
        print("🧪 Testing Lee Method Scanner")
        print("=" * 50)
        
        # Import the scanner
        from atlas_lee_method import AtlasLeeMethodRealtimeScanner, LeeMethodScanner
        
        print("✅ Successfully imported Lee Method components")
        
        # Create scanner instance
        scanner = LeeMethodScanner()
        print("✅ Created LeeMethodScanner instance")
        
        # Test market hours check
        is_market_hours = scanner._is_market_hours()
        print(f"📊 Market hours check: {is_market_hours}")
        
        # Test with sample market data
        test_symbol = "AAPL"
        sample_market_data = {
            "symbol": test_symbol,
            "price": 210.0,
            "volume": 15000000,
            "high": 212.0,
            "low": 208.0,
            "open": 209.0,
            "close": 210.0,
            "timestamp": datetime.now().isoformat()
        }
        
        print(f"\n🔍 Testing scan for {test_symbol} with sample data...")
        
        # Configure scanner for testing (allow scanning outside market hours)
        scanner.market_hours_only = False
        scanner.allow_weak_signals = True
        scanner.confidence_threshold = 0.3  # Lower threshold for testing
        
        print("⚙️ Configured scanner for testing (market_hours_only=False, allow_weak_signals=True)")
        
        # Attempt to scan the symbol
        signal = await scanner.scan_symbol(test_symbol, sample_market_data)
        
        if signal:
            print(f"🎯 Signal generated!")
            print(f"   Symbol: {signal.symbol}")
            print(f"   Confidence: {signal.confidence:.2%}")
            print(f"   Signal Type: {signal.signal_type}")
            print(f"   Signal Strength: {signal.signal_strength}")
            print(f"   Is Valid: {signal.is_valid}")
        else:
            print("❌ No signal generated")
            
        # Test the realtime scanner
        print(f"\n🔄 Testing AtlasLeeMethodRealtimeScanner...")
        realtime_scanner = AtlasLeeMethodRealtimeScanner()
        
        # Check scanner status
        status = realtime_scanner.get_scanner_status()
        print(f"📊 Scanner Status:")
        print(f"   Is Scanning: {status.get('is_scanning', False)}")
        print(f"   Active Signals: {status.get('active_signals', 0)}")
        print(f"   Scanner Type: {status.get('scanner_type', 'Unknown')}")
        
        # Get active signals
        active_signals = realtime_scanner.get_active_signals()
        print(f"📈 Active Signals: {len(active_signals.get('signals', []))}")
        
        # Test manual scan
        print(f"\n🔍 Testing manual scan of multiple symbols...")
        test_symbols = ["AAPL", "MSFT", "GOOGL"]
        
        # Configure the internal scanner for testing
        realtime_scanner.scanner.market_hours_only = False
        realtime_scanner.scanner.allow_weak_signals = True
        realtime_scanner.scanner.confidence_threshold = 0.3
        
        signals = await realtime_scanner.scan_multiple_symbols(test_symbols)
        print(f"📊 Manual scan results: {len(signals)} signals found")
        
        for signal in signals:
            print(f"   🎯 {signal.symbol}: {signal.confidence:.2%} confidence")
            
        print(f"\n✅ Lee Method scanner test completed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_lee_method_scanner())
